"use client";

import { usePathname } from "next/navigation";
import NavbarItem from "./NavbarItem";

interface NavigationItem {
  key: string;
  href: string;
  hasDropdown?: boolean;
}

// Shared navigation items configuration
export const navigationItems: NavigationItem[] = [
  { key: "locationMonitor", href: "/location-monitor" },
  { key: "focusedTrips", href: "/focused-trips" },
  { key: "myAssignedPorts", href: "/my-assigned-ports" },
  { key: "dashboard", href: "/dashboard" },
  { key: "configuration", href: "/configuration" },
  { key: "suspiciousTrips", href: "/suspicious-trips" },
  { key: "reports", href: "/reports", hasDropdown: true },
  { key: "ahmedTest", href: "/dummy/ahmed" },
];

export default function Navbar() {
  const pathname = usePathname();

  return (
    <nav className="hidden md:flex items-center space-x-1 rtl:space-x-reverse">
      {navigationItems.map((item) => {
        //const isActive = pathname === item.href;
        const isActive = pathname.startsWith(item.href);
        return <NavbarItem key={item.key} item={item} isActive={isActive} />;
      })}
    </nav>
  );
}
