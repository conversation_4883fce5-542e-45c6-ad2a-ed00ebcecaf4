/**
 * @fileoverview Google Map View Component
 * @description Component for displaying Google Maps with interactive markers and tooltips
 *
 * @features
 * - Load and display Google Maps
 * - Create and manage custom markers for different location types
 * - Interactive info windows for markers with click-outside-to-close functionality
 * - Support for Arabic and English languages with dynamic translation
 * - Performance optimization to avoid unnecessary marker recreation
 * - Automatic tooltip closing when clicking outside
 *
 * @dependencies
 * - Google Maps JavaScript API
 * - React Hooks (useRef, useEffect, useState, useCallback)
 */

"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";
import { Location, MapCenter } from "../../types/map";
import MapLoadingScreen from "./MapLoadingScreen";

/**
 * Props interface for GoogleMapView component
 *
 * @interface GoogleMapViewProps
 * @description Properties for Google Maps view component
 */
interface GoogleMapViewProps {
  /** Array of locations to display on the map */
  locations: Location[];
  /** Map center (latitude and longitude) */
  center: MapCenter;
  /** Map zoom level */
  zoom: number;
  /** Interface language - defaults to English */
  language?: "en" | "ar";
  /** Function called when clicking on a location */
  onLocationClick?: (location: Location) => void;
}

/**
 * Google Map View Component
 *
 * @description Google Maps view component with interactive markers
 * Handles Google Maps API loading and custom marker creation
 *
 * @param {GoogleMapViewProps} props - Component properties
 * @returns {JSX.Element} Map component or loading screen
 *
 * @features
 * - Automatic Google Maps API loading
 * - Custom markers based on location type
 * - Interactive info windows
 * - Performance optimization by avoiding unnecessary recreation
 * - Support for Arabic and English languages
 *
 * @example
 * ```tsx
 * <GoogleMapView
 *   locations={locations}
 *   center={{ lat: 24.7136, lng: 46.6753 }}
 *   zoom={6}
 *   language="ar"
 *   onLocationClick={(location) => console.log(location)}
 * />
 * ```
 */
const GoogleMapView: React.FC<GoogleMapViewProps> = ({
  locations,
  center,
  zoom,
  language = "en",
  onLocationClick,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const [activeInfoWindow, setActiveInfoWindow] =
    useState<google.maps.InfoWindow | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);

  // Generate tooltip content with current language
  const generateTooltipContent = useCallback(
    (location: Location) => {
      const getLocationTypeLabel = (type: string) => {
        const typeLabels = {
          airport: language === "ar" ? "مطار" : "Airport",
          seaport: language === "ar" ? "ميناء بحري" : "Seaport",
          landport: language === "ar" ? "ميناء بري" : "Land Port",
          police_station: language === "ar" ? "مركز شرطة" : "Police Station",
          checkpoint: language === "ar" ? "نقطة تفتيش" : "Checkpoint",
        };
        return (
          typeLabels[type as keyof typeof typeLabels] || type.replace("_", " ")
        );
      };

      return `
      <div style="padding: 12px; min-width: 220px; font-family: system-ui, -apple-system, sans-serif;">
        <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px; color: #1f2937; line-height: 1.2;">
          ${language === "ar" ? location.nameAr : location.name}
        </h3>
        <p style="font-size: 13px; color: #6b7280; margin-bottom: 6px; line-height: 1.4;">
          📍 ${
            language === "ar"
              ? location.descriptionAr || location.description
              : location.description || ""
          }
        </p>
        <div style="display: flex; align-items: center; margin-bottom: 6px;">
          <span style="font-size: 12px; color: #2563eb; font-weight: 500; background: #eff6ff; padding: 2px 8px; border-radius: 12px;">
            ${getLocationTypeLabel(location.type)}
          </span>
        </div>
        <p style="font-size: 12px; color: #059669; margin-top: 8px; font-weight: 500;">
          ${language === "ar" ? "الحالة:" : "Status:"}
          <span style="color: ${
            location.status === "active" ? "#059669" : "#dc2626"
          };">
            ${
              location.status === "active"
                ? language === "ar"
                  ? "نشط"
                  : "Active"
                : language === "ar"
                ? "غير نشط"
                : "Inactive"
            }
          </span>
        </p>
      </div>
    `;
    },
    [language]
  );

  // Load Google Maps API
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Check if already loaded
    if (window.google && window.google.maps) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      setIsLoaded(true);
    };

    script.onerror = () => {
      console.error("Failed to load Google Maps");
    };

    document.head.appendChild(script);

    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    const mapInstance = new google.maps.Map(mapRef.current, {
      center,
      zoom,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
    });

    setMap(mapInstance);
  }, [isLoaded, center, zoom]);

  // Create SVG marker icons
  const createMarkerIcon = useCallback((type: string) => {
    const iconConfigs = {
      airport: {
        color: "#1e40af",
        shadowColor: "#1e3a8a",
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M22 16v-2l-8.5-5V3.5c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5V9L2 14v2l8.5-2.5V19l-3 1.5V22l4.5-1 4.5 1v-1.5L13 19v-5.5L22 16z"/>
          <circle cx="12" cy="12" r="1" fill="white" opacity="0.8"/>
        </svg>`,
      },
      seaport: {
        color: "#0369a1",
        shadowColor: "#075985",
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M20 21c-1.39 0-2.78-.47-4-1.32-2.44 1.71-5.56 1.71-8 0C6.78 20.53 5.39 21 4 21H2v2h2c1.38 0 2.74-.35 4-.99 2.52 1.29 5.48 1.29 8 0 1.26.65 2.62.99 4 .99h2v-2h-2z"/>
          <path d="M4 19h.05l1.89-6.68c.08-.26.06-.54-.06-.78s-.32-.42-.58-.5L4 10.62V6c0-1.1.9-2 2-2h3V1h6v3h3c1.1 0 2 .9 2 2v4.62l1.29.42c.26.08.46.26.58.5s.14.52.06.78L20.05 19H20c-1.6 0-3.02-.88-4-2-.98 1.12-2.4 2-4 2s-3.02-.88-4-2c-.98 1.12-2.4 2-4 2z"/>
          <rect x="10" y="6" width="4" height="2" fill="white" opacity="0.7"/>
        </svg>`,
      },
      landport: {
        color: "#059669",
        shadowColor: "#047857",
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4z"/>
          <circle cx="6" cy="17" r="1.5" fill="white"/>
          <circle cx="18" cy="17" r="1.5" fill="white"/>
          <path d="M17 9.5h2.5l1.96 2.5H17V9.5z" fill="white" opacity="0.8"/>
          <rect x="3" y="6" width="14" height="2" fill="white" opacity="0.6"/>
        </svg>`,
      },
      police_station: {
        color: "#dc2626",
        shadowColor: "#b91c1c",
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
          <path d="M12 7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="white" opacity="0.9"/>
          <path d="M12 13c-1.5 0-3 .5-3 1.5V16h6v-1.5c0-1-.5-1.5-3-1.5z" fill="white" opacity="0.8"/>
        </svg>`,
      },
      checkpoint: {
        color: "#d97706",
        shadowColor: "#b45309",
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2z"/>
          <path d="M9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6z" fill="white" opacity="0.3"/>
          <circle cx="12" cy="15" r="2" fill="white"/>
          <rect x="11" y="17" width="2" height="2" fill="white" opacity="0.8"/>
        </svg>`,
      },
    };

    const config =
      iconConfigs[type as keyof typeof iconConfigs] || iconConfigs.checkpoint;

    return {
      url:
        "data:image/svg+xml;charset=UTF-8," +
        encodeURIComponent(`
        <svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
              <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="${config.shadowColor}" flood-opacity="0.3"/>
            </filter>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style="stop-color:${config.color};stop-opacity:1" />
              <stop offset="100%" style="stop-color:${config.shadowColor};stop-opacity:1" />
            </linearGradient>
          </defs>
          <circle cx="24" cy="24" r="20" fill="url(#gradient)" stroke="white" stroke-width="3" filter="url(#shadow)"/>
          <circle cx="24" cy="24" r="16" fill="none" stroke="white" stroke-width="1" opacity="0.3"/>
          <g transform="translate(12, 12) scale(0.5)">
            ${config.svg}
          </g>
        </svg>
      `),
      scaledSize: new google.maps.Size(48, 48),
      anchor: new google.maps.Point(24, 24),
    };
  }, []);

  // Create and manage markers with better performance
  useEffect(() => {
    if (!map) return;

    // Clear existing markers only if locations changed significantly
    const shouldRecreateMarkers =
      markers.length !== locations.length ||
      markers.some((marker, index) => {
        const location = locations[index];
        if (!location) return true;
        const position = marker.getPosition();
        return (
          !position ||
          position.lat() !== location.lat ||
          position.lng() !== location.lng
        );
      });

    if (!shouldRecreateMarkers) {
      // Just update existing markers' titles and info windows
      markers.forEach((marker, index) => {
        const location = locations[index];
        if (location) {
          marker.setTitle(language === "ar" ? location.nameAr : location.name);
        }
      });
      return;
    }

    // Clear existing markers
    markers.forEach((marker) => marker.setMap(null));

    if (!locations.length) {
      setMarkers([]);
      return;
    }

    // Create new markers
    const newMarkers = locations.map((location) => {
      const marker = new google.maps.Marker({
        position: { lat: location.lat, lng: location.lng },
        map: map,
        title: language === "ar" ? location.nameAr : location.name,
        icon: createMarkerIcon(location.type),
      });

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: generateTooltipContent(location),
      });

      marker.addListener("click", () => {
        // Close any existing info window
        if (activeInfoWindow) {
          activeInfoWindow.close();
        }

        // Open new info window
        infoWindow.open(map, marker);
        setActiveInfoWindow(infoWindow);
        setCurrentLocation(location);

        if (onLocationClick) {
          onLocationClick(location);
        }
      });

      return marker;
    });

    setMarkers(newMarkers);

    // Add click listener to map to close info windows when clicking outside
    if (map) {
      const mapClickListener = map.addListener("click", () => {
        if (activeInfoWindow) {
          activeInfoWindow.close();
          setActiveInfoWindow(null);
          setCurrentLocation(null);
        }
      });

      // Cleanup listener on next update
      return () => {
        google.maps.event.removeListener(mapClickListener);
      };
    }
  }, [
    map,
    locations,
    language,
    onLocationClick,
    createMarkerIcon,
    markers,
    activeInfoWindow,
    generateTooltipContent,
  ]);

  // Update active tooltip content when language changes
  useEffect(() => {
    if (activeInfoWindow && currentLocation) {
      activeInfoWindow.setContent(generateTooltipContent(currentLocation));
    }
  }, [language, activeInfoWindow, currentLocation, generateTooltipContent]);

  if (!isLoaded) {
    return <MapLoadingScreen language={language} />;
  }

  return <div ref={mapRef} className="w-full h-full" />;
};

export default GoogleMapView;
