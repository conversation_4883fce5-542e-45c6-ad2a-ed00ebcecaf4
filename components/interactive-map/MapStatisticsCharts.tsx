'use client';

import React from 'react';
import { TripStatistics } from '../../types/map';
import MapPieChart from './MapPieChart';
import MapBarChart from './MapBarChart';

interface MapStatisticsChartsProps {
  statistics: TripStatistics;
  language?: 'en' | 'ar';
}

const MapStatisticsCharts: React.FC<MapStatisticsChartsProps> = ({
  statistics,
  language = 'en'
}) => {
  return (
    <div className="space-y-6">
      {/* Pie Chart */}
      <div>
        <h4 className="text-sm font-medium mb-2">
          {language === 'ar' ? 'الرحلات النشطة' : 'Active Trips'}
        </h4>
        <MapPieChart
          data={statistics.pieData}
          language={language}
        />
      </div>

      {/* Bar Chart */}
      <div>
        <h4 className="text-sm font-medium mb-2">
          {language === 'ar' ? 'تحليل الطرق' : 'Route Analytics'}
        </h4>
        <MapBarChart
          data={statistics.barData}
          language={language}
        />
      </div>

      {/* Summary Statistics */}
      <div className="border-t pt-4">
        <h4 className="text-sm font-medium mb-3">
          {language === 'ar' ? 'ملخص الإحصائيات' : 'Summary Statistics'}
        </h4>
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="bg-blue-50 p-2 rounded">
            <div className="font-medium text-blue-800">
              {statistics.summary.totalActiveTrips}
            </div>
            <div className="text-blue-600">
              {language === 'ar' ? 'الرحلات النشطة' : 'Active Trips'}
            </div>
          </div>
          <div className="bg-green-50 p-2 rounded">
            <div className="font-medium text-green-800">
              {statistics.summary.totalCompletedTrips}
            </div>
            <div className="text-green-600">
              {language === 'ar' ? 'الرحلات المكتملة' : 'Completed Trips'}
            </div>
          </div>
          <div className="bg-red-50 p-2 rounded">
            <div className="font-medium text-red-800">
              {statistics.summary.totalAlerts}
            </div>
            <div className="text-red-600">
              {language === 'ar' ? 'إجمالي التنبيهات' : 'Total Alerts'}
            </div>
          </div>
          <div className="bg-yellow-50 p-2 rounded">
            <div className="font-medium text-yellow-800">
              {statistics.summary.onTimePercentage}%
            </div>
            <div className="text-yellow-600">
              {language === 'ar' ? 'في الوقت المحدد' : 'On Time'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapStatisticsCharts;
