// components/ClockComponent.tsx
'use client';
import React, { useState, useEffect } from 'react';
import { Clock, Calendar, Settings } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Button } from '@/components/ui/button';

// import { Button } from '../ui/button';
// Hijri date conversion utility
class HijriDate {
  static toHijri(gregorianDate: Date): { year: number; month: number; day: number; monthName: string } {
    // Simple Hijri conversion algorithm (approximation)
    const gregorianYear = gregorianDate.getFullYear();
    const gregorianMonth = gregorianDate.getMonth() + 1;
    const gregorianDay = gregorianDate.getDate();
    
    // Convert Gregorian to Julian Day Number
    const a = Math.floor((14 - gregorianMonth) / 12);
    const y = gregorianYear - a;
    const m = gregorianMonth + 12 * a - 3;
    const jdn = gregorianDay + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) + 1721119;
    
    // Convert Julian Day Number to Hijri
    const hijriJdn = jdn - 1948440; // Hijri epoch adjustment
    const hijriYear = Math.floor((30 * hijriJdn + 10646) / 10631);
    const hijriMonth = Math.min(12, Math.ceil((hijriJdn - 29 - Math.floor((hijriYear - 1) * 354) - Math.floor((3 * hijriYear + 3) / 4)) / 29.5) + 1);
    const hijriDay = hijriJdn - Math.floor((hijriYear - 1) * 354) - Math.floor((3 * hijriYear + 3) / 4) - Math.floor((hijriMonth - 1) * 29.5) + 1;
    
    const hijriMonths = [
      'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
      'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];
    
    // Hijri months in English are defined inline below
    
    return {
      year: Math.max(1, hijriYear),
      month: Math.max(1, Math.min(12, hijriMonth)),
      day: Math.max(1, Math.min(30, Math.floor(hijriDay))),
      monthName: hijriMonths[Math.max(0, Math.min(11, hijriMonth - 1))]
    };
  }
}
interface ClockCardProps {
  variant?: 'default' | 'compact' | 'detailed';
  showSettings?: boolean;
  className?: string;//دي لو حابب تضيف CSS class علشان تغير شكل التصميم.
  // UI Control Props - control what features are visible to end users
  enableTimezoneControl?: boolean;
  enableDateFormatControl?: boolean;
  enableTimeFormatControl?: boolean;
  enableDisplayStyleControl?: boolean;
}

type DateFormat = 'both' | 'gregorian' | 'hijri';
type TimeFormat = '12h' | '24h';
type DisplayStyle = 'digital' | 'analog' | 'minimal';
type Timezone = string;

// Common timezones
const TIMEZONES = [
  { value: 'Asia/Riyadh', label: 'Riyadh (GMT+3)', labelAr: 'الرياض (GMT+3)' },
  { value: 'Asia/Dubai', label: 'Dubai (GMT+4)', labelAr: 'دبي (GMT+4)' },
  { value: 'Asia/Kuwait', label: 'Kuwait (GMT+3)', labelAr: 'الكويت (GMT+3)' },
  { value: 'Asia/Qatar', label: 'Doha (GMT+3)', labelAr: 'الدوحة (GMT+3)' },
  { value: 'Asia/Bahrain', label: 'Manama (GMT+3)', labelAr: 'المنامة (GMT+3)' },
  { value: 'Europe/London', label: 'London (GMT+0)', labelAr: 'لندن (GMT+0)' },
  { value: 'America/New_York', label: 'New York (GMT-5)', labelAr: 'نيويورك (GMT-5)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)', labelAr: 'طوكيو (GMT+9)' },
  { value: 'Australia/Sydney', label: 'Sydney (GMT+11)', labelAr: 'سيدني (GMT+11)' }
];

export default function ClockCard({ 
  variant = 'default', 
  showSettings = true,
  className = '',
  // UI Control Props - default to true (all features enabled)
  enableTimezoneControl = true,
  enableDateFormatControl = true,
  enableTimeFormatControl = true,
  enableDisplayStyleControl = true
}: ClockCardProps) {
  const { language } = useLanguage();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [dateFormat, setDateFormat] = useState<DateFormat>('both');
  const [timeFormat, setTimeFormat] = useState<TimeFormat>('24h');
  const [displayStyle, setDisplayStyle] = useState<DisplayStyle>('digital');
  const [timezone, setTimezone] = useState<Timezone>('Asia/Riyadh');
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Prevent hydration mismatch by showing static time until mounted
  if (!isMounted) {
    return (
      <div className={`
        ${variant === 'compact' ? 'p-4' : 'p-6'} 
        bg-white rounded-lg shadow-md border border-gray-200 
        ${className}
      `}>
        <div className="text-center">
          <div className="text-4xl font-bold text-blue-900 mb-4 font-mono">
            --:--:--
          </div>
          <div className="text-gray-600">
            Loading...
          </div>
        </div>
      </div>
    );
  }
  // Get time in selected timezone
  const getTimeInTimezone = (date: Date): Date => {
    return new Date(date.toLocaleString('en-US', { timeZone: timezone }));
  };

  const formatTime = (date: Date): string => {
    const timeInTimezone = getTimeInTimezone(date);
    if (timeFormat === '12h') {
      return timeInTimezone.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
        timeZone: timezone
      });
    } else {
      return timeInTimezone.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: timezone
      });
    }
  };
   const formatGregorianDate = (date: Date): string => {
    return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: timezone
    });
  };

  const formatHijriDate = (date: Date): string => {
    const timeInTimezone = getTimeInTimezone(date);
    const hijri = HijriDate.toHijri(timeInTimezone);
    if (language === 'ar') {
      return `${hijri.day} ${hijri.monthName} ${hijri.year} هـ`;
    } else {
      const hijriMonthsEn = [
        'Muharram', 'Safar', 'Rabi\'al-Awwal', 'Rabi\'al-Thani', 'Jumada al-Awwal', 'Jumada al-Thani',
        'Rajab', 'Sha\'ban', 'Ramadan', 'Shawwal', 'Dhu al-Qi\'dah', 'Dhu al-Hijjah'
      ];
      return `${hijri.day} ${hijriMonthsEn[hijri.month - 1]} ${hijri.year} AH`;
    }
  };
  const renderAnalogClock = () => {
    const timeInTimezone = getTimeInTimezone(currentTime);
    const hours = timeInTimezone.getHours() % 12;
    const minutes = timeInTimezone.getMinutes();
    const seconds = timeInTimezone.getSeconds();
    
    const hourAngle = (hours * 30) + (minutes * 0.5);
    const minuteAngle = minutes * 6;
    const secondAngle = seconds * 6;

    return (
      <div className="relative w-32 h-32 mx-auto mb-4">
        <div className="absolute inset-0 rounded-full border-4 border-gray-300 bg-white">
          {/* Hour markers */}
          {[...Array(12)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-6 bg-gray-400"
              style={{
                top: '8px',
                left: '50%',
                transformOrigin: '50% 56px',
                transform: `translateX(-50%) rotate(${i * 30}deg)`
              }}
            />
          ))}
          
          {/* Hour hand */}
          <div
            className="absolute w-1 bg-gray-800 rounded-full"
            style={{
              height: '40px',
              top: '24px',
              left: '50%',
              transformOrigin: '50% 40px',
              transform: `translateX(-50%) rotate(${hourAngle}deg)`
            }}
          />
          
          {/* Minute hand */}
          <div
            className="absolute w-0.5 bg-gray-600 rounded-full"
            style={{
              height: '50px',
              top: '14px',
              left: '50%',
              transformOrigin: '50% 50px',
              transform: `translateX(-50%) rotate(${minuteAngle}deg)`
            }}
          />
          
          {/* Second hand */}
          <div
            className="absolute w-0.5 bg-red-500 rounded-full"
            style={{
              height: '55px',
              top: '9px',
              left: '50%',
              transformOrigin: '50% 55px',
              transform: `translateX(-50%) rotate(${secondAngle}deg)`
            }}
          />
          
          {/* Center dot */}
          <div className="absolute w-3 h-3 bg-gray-800 rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </div>
      </div>
    );
  };
const renderSettingsPanel = () => {
    if (!showSettingsPanel) return null;

    return (
      <div className="absolute top-full left-0 right-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
        <div className="space-y-4">
          {enableDateFormatControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'تنسيق التاريخ' : 'Date Format'}
              </label>
              <select
                value={dateFormat}
                onChange={(e) => setDateFormat(e.target.value as DateFormat)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="both">{language === 'ar' ? 'هجري وميلادي' : 'Both Hijri & Gregorian'}</option>
                <option value="gregorian">{language === 'ar' ? 'ميلادي فقط' : 'Gregorian Only'}</option>
                <option value="hijri">{language === 'ar' ? 'هجري فقط' : 'Hijri Only'}</option>
              </select>
            </div>
          )}
          
          {enableTimeFormatControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'تنسيق الوقت' : 'Time Format'}
              </label>
              <select
                value={timeFormat}
                onChange={(e) => setTimeFormat(e.target.value as TimeFormat)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="24h">{language === 'ar' ? '24 ساعة' : '24 Hour'}</option>
                <option value="12h">{language === 'ar' ? '12 ساعة' : '12 Hour'}</option>
              </select>
            </div>
          )}
          
          {enableDisplayStyleControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'نمط العرض' : 'Display Style'}
              </label>
              <select
                value={displayStyle}
                onChange={(e) => setDisplayStyle(e.target.value as DisplayStyle)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="digital">{language === 'ar' ? 'رقمي' : 'Digital'}</option>
                <option value="analog">{language === 'ar' ? 'تناظري' : 'Analog'}</option>
                <option value="minimal">{language === 'ar' ? 'مبسط' : 'Minimal'}</option>
              </select>
            </div>
          )}
          
          {enableTimezoneControl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'المنطقة الزمنية' : 'Timezone'}
              </label>
              <select
                value={timezone}
                onChange={(e) => setTimezone(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {TIMEZONES.map((tz) => (
                  <option key={tz.value} value={tz.value}>
                    {language === 'ar' ? tz.labelAr : tz.label}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
    );
  };

  const cardVariants = {
    default: 'p-6',
    compact: 'p-4',
    detailed: 'p-8'
  };

  return (
    <div className={`
      relative bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-lg 
      transition-all duration-200 hover:shadow-lg hover:border-blue-300
      ${cardVariants[variant]} ${className}
    `}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-600 text-white rounded-full">
            <Clock className="w-5 h-5" />
          </div>
          <h3 className="text-lg font-semibold text-blue-900">
            {language === 'ar' ? 'الساعة والتاريخ' : 'Clock & Date'}
          </h3>
        </div>
        
        {showSettings && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettingsPanel(!showSettingsPanel)}
            className="text-blue-600 hover:text-blue-800"
          >
            <Settings className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Clock Display */}
      <div className="text-center">
        {displayStyle === 'analog' && renderAnalogClock()}
        
        {displayStyle !== 'analog' && (
          <div className={`text-4xl font-bold text-blue-900 mb-4 font-mono ${
            displayStyle === 'minimal' ? 'text-2xl' : ''
          }`}>
            {formatTime(currentTime)}
          </div>
        )}

        {/* Date Display */}
        {displayStyle !== 'minimal' && (
          <div className="space-y-2">
            {(dateFormat === 'both' || dateFormat === 'gregorian') && (
              <div className="flex items-center justify-center space-x-2 text-blue-800">
                <Calendar className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {formatGregorianDate(currentTime)}
                </span>
              </div>
            )}
            
            {(dateFormat === 'both' || dateFormat === 'hijri') && (
              <div className="flex items-center justify-center space-x-2 text-blue-700">
                <Calendar className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {formatHijriDate(currentTime)}
                </span>
              </div>
            )}
          </div>
        )}
        
        {displayStyle === 'minimal' && (
          <div className="text-sm text-blue-700">
            {dateFormat === 'hijri' ? formatHijriDate(currentTime) : formatGregorianDate(currentTime)}
          </div>
        )}
      </div>

      {/* Settings Panel */}
      {renderSettingsPanel()}
    </div>
  );
}





