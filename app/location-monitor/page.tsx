"use client";

import React from "react";
import InteractiveMapContainer from "../../components/interactive-map/InteractiveMapContainer";
import { Location, Alert, Route } from "../../types/map";

export default function LocationMonitorPage() {
  // Event handlers for location monitoring
  const handleLocationClick = (location: Location) => {
    console.log("Location Monitor - Location clicked:", location);
    // Add location-specific monitoring logic
  };

  const handleAlertClick = (alert: Alert) => {
    console.log("Location Monitor - Alert clicked:", alert);
    // Add alert handling for monitoring
  };

  const handleRouteSelect = (routes: Route[]) => {
    console.log("Location Monitor - Routes selected:", routes);
    // Add route monitoring logic
  };

  return (
    <div className="w-full" style={{ height: "calc(100vh - 112px - 40px)" }}>
      <InteractiveMapContainer
        pageType="location-monitor"
        initialCenter={{ lat: 24.7136, lng: 46.6753 }}
        initialZoom={6}
        showSidebar={true}
        onLocationClick={handleLocationClick}
        onAlertClick={handleAlertClick}
        onRouteSelect={handleRouteSelect}
      />
    </div>
  );
}
