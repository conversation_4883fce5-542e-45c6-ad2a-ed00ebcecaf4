import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "../contexts/LanguageContext";
import { <PERSON><PERSON>, <PERSON>er } from "../components/layout";
// import { Breadcrumb } from "../components/layout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "TTS Template - Government Portal",
  description: "Text-to-Speech Template for Government Applications",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
        suppressHydrationWarning={true}
      >
        <LanguageProvider>
          <Header />
          {/* <Breadcrumb /> */}
          <main className="flex-1 pt-12 md:pt-[128px] pb-16">{children}</main>
          <Footer />
        </LanguageProvider>
      </body>
    </html>
  );
}
