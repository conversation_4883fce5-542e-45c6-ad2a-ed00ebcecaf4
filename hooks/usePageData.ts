/**
 * @fileoverview Page Data Management Hook
 * @description Custom hook for managing different data sets based on page type
 *
 * @features
 * - Dynamic data loading based on page type (default, location-monitor, focused-trips)
 * - Data caching to improve performance and avoid unnecessary re-loads
 * - Loading and error state management
 * - Automatic page type detection from URL pathname
 * - Data refetch functionality
 * - Type-safe data structures
 *
 * @usage
 * ```tsx
 * // Basic usage with specific page type
 * const { locations, alerts, routes, statistics, loading, error } = usePageData('location-monitor');
 *
 * // Auto-detect page type from URL
 * const mapData = useAutoPageData();
 *
 * // Manual refetch
 * const { refetch } = usePageData('focused-trips');
 * await refetch();
 * ```
 */

import { useState, useEffect, useMemo } from "react";
import {
  Location,
  Alert,
  Route,
  TripStatistics,
  UseMapData,
} from "../types/map";

// Import different data sets
import defaultLocationsData from "../data/locations.json";
import defaultAlertsData from "../data/alerts.json";
import defaultRoutesData from "../data/routes.json";
import defaultStatisticsData from "../data/statistics.json";

import locationMonitorData from "../data/location-monitor-data.json";
import focusedTripsData from "../data/focused-trips-data.json";
import dummyAhmedData from "../data/dummy-ahmed-data.json";

export type PageType =
  | "default"
  | "location-monitor"
  | "focused-trips"
  | "dummy-ahmed";

interface PageDataSets {
  [key: string]: {
    locations: Location[];
    alerts: Alert[];
    routes: Route[];
    statistics: TripStatistics;
  };
}

// Cache for different page data sets
let pageDataCache: PageDataSets = {};

export const usePageData = (pageType: PageType = "default"): UseMapData => {
  const [loading, setLoading] = useState(!pageDataCache[pageType]);
  const [error, setError] = useState<string | null>(null);

  // Load data based on page type
  const data = useMemo(() => {
    if (pageDataCache[pageType]) {
      return pageDataCache[pageType];
    }

    try {
      let newData: {
        locations: Location[];
        alerts: Alert[];
        routes: Route[];
        statistics: TripStatistics;
      };

      // Default statistics structure for fallback
      const defaultStatistics: TripStatistics = {
        pieData: [],
        barData: [],
        summary: {
          totalActiveTrips: 0,
          totalCompletedTrips: 0,
          totalAlerts: 0,
          communicationLostTrips: 0,
          averageDelay: 0,
          onTimePercentage: 0,
          lastUpdated: new Date().toISOString(),
        },
      };

      switch (pageType) {
        case "location-monitor":
          newData = {
            locations: (locationMonitorData.locations as Location[]) || [],
            alerts: (locationMonitorData.alerts as Alert[]) || [],
            routes: (locationMonitorData.routes as Route[]) || [],
            statistics:
              (locationMonitorData.statistics as TripStatistics) ||
              defaultStatistics,
          };
          break;

        case "focused-trips":
          newData = {
            locations: (focusedTripsData.locations as Location[]) || [],
            alerts: (focusedTripsData.alerts as Alert[]) || [],
            routes: (focusedTripsData.routes as Route[]) || [],
            statistics:
              (focusedTripsData.statistics as TripStatistics) ||
              defaultStatistics,
          };
          break;

        case "dummy-ahmed":
          newData = {
            locations: (dummyAhmedData.locations as Location[]) || [],
            alerts: (dummyAhmedData.alerts as Alert[]) || [],
            routes: (dummyAhmedData.routes as Route[]) || [],
            statistics:
              (dummyAhmedData.statistics as TripStatistics) ||
              defaultStatistics,
          };
          break;

        case "default":
        default:
          newData = {
            locations: (defaultLocationsData.locations as Location[]) || [],
            alerts: (defaultAlertsData.alerts as Alert[]) || [],
            routes: (defaultRoutesData.routes as Route[]) || [],
            statistics:
              (defaultStatisticsData.tripStatistics as TripStatistics) ||
              defaultStatistics,
          };
          break;
      }

      pageDataCache[pageType] = newData;
      return newData;
    } catch (err) {
      console.error(`Error loading ${pageType} data:`, err);
      setError(
        err instanceof Error ? err.message : `Failed to load ${pageType} data`
      );
      return {
        locations: [],
        alerts: [],
        routes: [],
        statistics: {
          pieData: [],
          barData: [],
          summary: {
            totalActiveTrips: 0,
            totalCompletedTrips: 0,
            totalAlerts: 0,
            communicationLostTrips: 0,
            averageDelay: 0,
            onTimePercentage: 0,
            lastUpdated: new Date().toISOString(),
          },
        },
      };
    }
  }, [pageType]);

  const refetch = async () => {
    // Clear cache for this page type and reload
    delete pageDataCache[pageType];
    setLoading(true);
    setError(null);

    // Small delay to show loading state
    await new Promise((resolve) => setTimeout(resolve, 100));

    try {
      // Data will be reloaded in the next render due to useMemo dependency
      setLoading(false);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : `Failed to reload ${pageType} data`
      );
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!pageDataCache[pageType]) {
      // Small delay to show loading state on first load
      const timer = setTimeout(() => {
        setLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setLoading(false);
    }
  }, [pageType]);

  return {
    locations: data.locations,
    alerts: data.alerts,
    routes: data.routes,
    statistics: data.statistics,
    loading,
    error,
    refetch,
  };
};

// Helper function to get page type from pathname
export const getPageTypeFromPath = (pathname: string): PageType => {
  if (pathname.includes("location-monitor")) {
    return "location-monitor";
  }
  if (pathname.includes("focused-trips")) {
    return "focused-trips";
  }
  if (pathname.includes("dummy/ahmed")) {
    return "dummy-ahmed";
  }
  return "default";
};

// Hook that automatically detects page type from current URL
export const useAutoPageData = (): UseMapData => {
  const [pageType, setPageType] = useState<PageType>("default");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const currentPageType = getPageTypeFromPath(window.location.pathname);
      setPageType(currentPageType);
    }
  }, []);

  return usePageData(pageType);
};
