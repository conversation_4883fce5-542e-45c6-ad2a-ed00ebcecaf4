import { useState, useEffect } from 'react';

export type Language = 'en' | 'ar';

export const useLanguage = (): Language => {
  const [language, setLanguage] = useState<Language>('en');

  useEffect(() => {
    // Check for language in various sources
    const detectLanguage = (): Language => {
      // 1. Check URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const urlLang = urlParams.get('lang');
      if (urlLang === 'ar' || urlLang === 'en') {
        return urlLang as Language;
      }

      // 2. Check localStorage
      const storedLang = localStorage.getItem('language');
      if (storedLang === 'ar' || storedLang === 'en') {
        return storedLang as Language;
      }

      // 3. Check document direction (RTL/LTR)
      const docDir = document.documentElement.dir;
      if (docDir === 'rtl') {
        return 'ar';
      }

      // 4. Check document lang attribute
      const docLang = document.documentElement.lang;
      if (docLang.startsWith('ar')) {
        return 'ar';
      }

      // 5. Check browser language
      const browserLang = navigator.language || navigator.languages?.[0];
      if (browserLang?.startsWith('ar')) {
        return 'ar';
      }

      // 6. Check for Arabic content in the page
      const hasArabicContent = document.body.textContent?.match(/[\u0600-\u06FF]/);
      if (hasArabicContent) {
        return 'ar';
      }

      // Default to English
      return 'en';
    };

    const detectedLanguage = detectLanguage();
    setLanguage(detectedLanguage);

    // Listen for language changes in localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'language' && (e.newValue === 'ar' || e.newValue === 'en')) {
        setLanguage(e.newValue as Language);
      }
    };

    // Listen for document direction changes
    const handleDirectionChange = () => {
      const newLang = document.documentElement.dir === 'rtl' ? 'ar' : 'en';
      setLanguage(newLang);
    };

    // Listen for document lang attribute changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'lang' || mutation.attributeName === 'dir')) {
          const newLang = detectLanguage();
          setLanguage(newLang);
        }
      });
    });

    window.addEventListener('storage', handleStorageChange);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['lang', 'dir']
    });

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      observer.disconnect();
    };
  }, []);

  return language;
};

// Hook for setting language (optional - for components that need to change language)
export const useLanguageSetter = () => {
  const setLanguage = (lang: Language) => {
    // Update localStorage
    localStorage.setItem('language', lang);
    
    // Update document attributes
    document.documentElement.lang = lang === 'ar' ? 'ar-SA' : 'en-US';
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    
    // Dispatch storage event for other components
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'language',
      newValue: lang,
      oldValue: localStorage.getItem('language')
    }));
  };

  return setLanguage;
};
