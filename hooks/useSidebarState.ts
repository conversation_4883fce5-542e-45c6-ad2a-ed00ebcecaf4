import { useState } from 'react';
import { SidebarTab, SearchFilters, RouteFilters, UseSidebarState } from '../types/map';

const initialSearchFilters: SearchFilters = {
  showPort: true,
  showCheckpost: true,
  tripCode: 'Trip Code',
  searchValue: ''
};

const initialRouteFilters: RouteFilters = {
  filter: 'my-routes',
  selectedRoutes: [],
  searchTerm: ''
};

export const useSidebarState = (): UseSidebarState => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<SidebarTab>('search');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>(initialSearchFilters);
  const [routeFilters, setRouteFilters] = useState<RouteFilters>(initialRouteFilters);

  const updateSearchFilters = (filters: Partial<SearchFilters>) => {
    setSearchFilters(prev => ({ ...prev, ...filters }));
  };

  const updateRouteFilters = (filters: Partial<RouteFilters>) => {
    setRouteFilters(prev => ({ ...prev, ...filters }));
  };

  const resetFilters = () => {
    setSearchFilters(initialSearchFilters);
    setRouteFilters(initialRouteFilters);
  };

  return {
    isOpen,
    activeTab,
    searchFilters,
    routeFilters,
    setIsOpen,
    setActiveTab,
    updateSearchFilters,
    updateRouteFilters,
    resetFilters
  };
};

// Hook for managing route selection
export const useRouteSelection = (initialRoutes: string[] = []) => {
  const [selectedRoutes, setSelectedRoutes] = useState<string[]>(initialRoutes);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);

  const addRoute = (route: string) => {
    if (!selectedRoutes.includes(route)) {
      setSelectedRoutes(prev => [...prev, route]);
    }
    setSearchTerm('');
    setShowSuggestions(false);
  };

  const removeRoute = (index: number) => {
    setSelectedRoutes(prev => prev.filter((_, i) => i !== index));
  };

  const clearRoutes = () => {
    setSelectedRoutes([]);
  };

  const setRoutes = (routes: string[]) => {
    setSelectedRoutes(routes);
  };

  return {
    selectedRoutes,
    searchTerm,
    showSuggestions,
    setSearchTerm,
    setShowSuggestions,
    addRoute,
    removeRoute,
    clearRoutes,
    setRoutes
  };
};

// Hook for managing alert filters
export const useAlertFilters = () => {
  const [severityFilter, setSeverityFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [readFilter, setReadFilter] = useState<boolean | undefined>(undefined);

  const resetAlertFilters = () => {
    setSeverityFilter('');
    setTypeFilter('');
    setReadFilter(undefined);
  };

  return {
    severityFilter,
    typeFilter,
    readFilter,
    setSeverityFilter,
    setTypeFilter,
    setReadFilter,
    resetAlertFilters
  };
};
