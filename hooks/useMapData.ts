import { useState, useEffect, useMemo } from "react";
import {
  Location,
  Alert,
  Route,
  TripStatistics,
  UseMapData,
} from "../types/map";

// Import JSON data
import locationsData from "../data/locations.json";
import alertsData from "../data/alerts.json";
import routesData from "../data/routes.json";
import statisticsData from "../data/statistics.json";

// Cache for loaded data to prevent re-loading
let dataCache: {
  locations: Location[];
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
} | null = null;

export const useMapData = (): UseMapData => {
  const [loading, setLoading] = useState(!dataCache);
  const [error, setError] = useState<string | null>(null);

  // Use cached data if available, otherwise load from JSON
  const data = useMemo(() => {
    if (dataCache) {
      return dataCache;
    }

    try {
      const newData = {
        locations: locationsData.locations as Location[],
        alerts: alertsData.alerts as Alert[],
        routes: routesData.routes as Route[],
        statistics: statisticsData.tripStatistics as TripStatistics,
      };

      dataCache = newData;
      return newData;
    } catch (err) {
      console.error("Error loading map data:", err);
      setError(err instanceof Error ? err.message : "Failed to load data");
      return {
        locations: [],
        alerts: [],
        routes: [],
        statistics: {
          pieData: [],
          barData: [],
          summary: {
            totalActiveTrips: 0,
            totalCompletedTrips: 0,
            totalAlerts: 0,
            communicationLostTrips: 0,
            averageDelay: 0,
            onTimePercentage: 0,
            lastUpdated: new Date().toISOString(),
          },
        },
      };
    }
  }, []);

  const refetch = async () => {
    // Clear cache and reload
    dataCache = null;
    setLoading(true);
    setError(null);

    // Small delay to show loading state
    await new Promise((resolve) => setTimeout(resolve, 100));

    try {
      const newData = {
        locations: locationsData.locations as Location[],
        alerts: alertsData.alerts as Alert[],
        routes: routesData.routes as Route[],
        statistics: statisticsData.tripStatistics as TripStatistics,
      };

      dataCache = newData;
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to reload data");
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!dataCache) {
      // Small delay to show loading state on first load
      const timer = setTimeout(() => {
        setLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setLoading(false);
    }
  }, []);

  return {
    locations: data.locations,
    alerts: data.alerts,
    routes: data.routes,
    statistics: data.statistics,
    loading,
    error,
    refetch,
  };
};

// Hook for filtering locations based on search criteria
export const useFilteredLocations = (
  locations: Location[],
  showPort: boolean,
  showCheckpost: boolean,
  searchValue: string
) => {
  return useMemo(() => {
    return locations.filter((location) => {
      // Filter by type
      const typeFilter =
        (showPort &&
          ["airport", "seaport", "landport"].includes(location.type)) ||
        (showCheckpost &&
          ["checkpoint", "police_station"].includes(location.type));

      if (!typeFilter) return false;

      // Filter by search value
      if (searchValue.trim()) {
        const searchLower = searchValue.toLowerCase();
        return (
          location.name.toLowerCase().includes(searchLower) ||
          location.nameAr.includes(searchValue) ||
          location.description?.toLowerCase().includes(searchLower) ||
          location.descriptionAr?.includes(searchValue)
        );
      }

      return true;
    });
  }, [locations, showPort, showCheckpost, searchValue]);
};

// Hook for filtering alerts
export const useFilteredAlerts = (
  alerts: Alert[],
  severity?: string,
  type?: string,
  isRead?: boolean
) => {
  return useMemo(() => {
    return alerts.filter((alert) => {
      if (severity && alert.severity !== severity) return false;
      if (type && alert.type !== type) return false;
      if (isRead !== undefined && alert.isRead !== isRead) return false;
      return true;
    });
  }, [alerts, severity, type, isRead]);
};

// Hook for filtering routes
export const useFilteredRoutes = (
  routes: Route[],
  filter: "my-routes" | "all",
  searchTerm: string
) => {
  return useMemo(() => {
    let filteredRoutes = routes;

    // Filter by user routes
    if (filter === "my-routes") {
      filteredRoutes = routes.filter((route) => route.isUserRoute);
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filteredRoutes = filteredRoutes.filter(
        (route) =>
          route.name.toLowerCase().includes(searchLower) ||
          route.nameAr.includes(searchTerm)
      );
    }

    return filteredRoutes;
  }, [routes, filter, searchTerm]);
};
